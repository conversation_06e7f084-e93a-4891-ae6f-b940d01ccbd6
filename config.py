"""
Configuration file for Geometry Dash AI
Adjust these parameters to tune the AI's behavior
"""

# Environment Configuration
ENV_CONFIG = {
    'frame_skip': 4,                    # Number of frames to skip between actions
    'max_episode_steps': 2000,          # Maximum steps per episode
    'reward_scale': 1.0,                # Scale factor for rewards
    'death_detection_enabled': True,    # Enable automatic death detection
    'max_stuck_frames': 30,             # Frames before considering player stuck
}

# Agent Configuration
AGENT_CONFIG = {
    'learning_rate': 0.001,             # Learning rate for neural network
    'gamma': 0.95,                      # Discount factor for future rewards
    'epsilon_start': 1.0,               # Initial exploration rate
    'epsilon_min': 0.01,                # Minimum exploration rate
    'epsilon_decay': 0.995,             # Exploration decay rate
    'batch_size': 32,                   # Training batch size
    'memory_capacity': 10000,           # Experience replay buffer size
    'target_update_freq': 100,          # Update target network every N steps
    'hidden_size': 128,                 # Neural network hidden layer size
}

# Training Configuration
TRAINING_CONFIG = {
    'episodes': 1000,                   # Default number of training episodes
    'save_interval': 100,               # Save model every N episodes
    'print_interval': 10,               # Print progress every N episodes
    'plot_interval': 50,                # Update plots every N episodes
}

# Screen Capture Configuration
CAPTURE_CONFIG = {
    'target_size': (84, 84),           # Target size for processed frames
    'capture_fps': 60,                  # Target capture frame rate
}

# Distance Sensor Configuration
SENSOR_CONFIG = {
    'num_rays': 6,                      # Number of distance sensor rays
    'max_distance': 150,                # Maximum sensor distance (pixels)
    'sensor_angles': [                  # Sensor angles in degrees
        0,      # Forward
        -15,    # Forward-up
        15,     # Forward-down
        -30,    # Up
        30,     # Down
        -45,    # Sharp up
    ]
}

# Reward Configuration
REWARD_CONFIG = {
    'survival_reward': 0.1,             # Reward per frame survived
    'progress_bonus': 0.05,             # Bonus for forward progress
    'death_penalty': -10.0,             # Penalty for dying
    'stuck_penalty': -0.1,              # Penalty for being stuck
    'backwards_penalty': -0.02,         # Penalty for going backwards
}

# File Paths
PATHS = {
    'models_dir': 'models',
    'plots_dir': 'plots',
    'logs_dir': 'logs',
    'best_model': 'models/best_model.pth',
    'checkpoint_prefix': 'models/checkpoint_ep',
    'final_model': 'models/final_model.pth',
}

# System Configuration
SYSTEM_CONFIG = {
    'device': 'cpu',                    # Force CPU usage (change to 'cuda' if available)
    'num_threads': 4,                   # Number of CPU threads to use
    'emergency_stop_key': 'esc',        # Emergency stop key
}

# Hyperparameter Presets
PRESETS = {
    'fast_learning': {
        'learning_rate': 0.003,
        'epsilon_decay': 0.99,
        'batch_size': 64,
        'target_update_freq': 50,
    },
    'stable_learning': {
        'learning_rate': 0.0005,
        'epsilon_decay': 0.998,
        'batch_size': 32,
        'target_update_freq': 200,
    },
    'exploration_focused': {
        'epsilon_start': 1.0,
        'epsilon_min': 0.05,
        'epsilon_decay': 0.9995,
    },
    'exploitation_focused': {
        'epsilon_start': 0.5,
        'epsilon_min': 0.001,
        'epsilon_decay': 0.99,
    }
}


def get_config(preset=None):
    """
    Get configuration with optional preset
    
    Args:
        preset: Optional preset name ('fast_learning', 'stable_learning', etc.)
    
    Returns:
        Dictionary with all configuration parameters
    """
    config = {
        'env': ENV_CONFIG.copy(),
        'agent': AGENT_CONFIG.copy(),
        'training': TRAINING_CONFIG.copy(),
        'capture': CAPTURE_CONFIG.copy(),
        'sensor': SENSOR_CONFIG.copy(),
        'reward': REWARD_CONFIG.copy(),
        'paths': PATHS.copy(),
        'system': SYSTEM_CONFIG.copy(),
    }
    
    # Apply preset if specified
    if preset and preset in PRESETS:
        preset_config = PRESETS[preset]
        config['agent'].update(preset_config)
        print(f"Applied preset: {preset}")
    
    return config


def print_config(config=None):
    """Print current configuration"""
    if config is None:
        config = get_config()
    
    print("🔧 Current Configuration:")
    print("=" * 50)
    
    for section, params in config.items():
        print(f"\n{section.upper()}:")
        for key, value in params.items():
            print(f"  {key}: {value}")
    
    print("=" * 50)


if __name__ == "__main__":
    # Print default configuration
    print_config()
    
    # Show available presets
    print("\n📋 Available Presets:")
    for preset_name, preset_config in PRESETS.items():
        print(f"  {preset_name}: {list(preset_config.keys())}")
