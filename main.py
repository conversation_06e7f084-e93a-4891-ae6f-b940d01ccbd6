#!/usr/bin/env python3
"""
Geometry Dash AI - Main Training Script
"""

import argparse
import torch
import signal
import sys
from geometry_dash_ai.environment import GeometryDashEnv
from geometry_dash_ai.agent import DQNAgent
from geometry_dash_ai.trainer import Trainer
from geometry_dash_ai.input_controller import emergency_stop
from config import get_config

def signal_handler(signum, frame):
    """Handle SIGTERM gracefully"""
    print("\n🚨 Received termination signal - cleaning up...")
    emergency_stop.cleanup_and_exit()

def main():
    # Setup signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    parser = argparse.ArgumentParser(description='Train AI to play Geometry Dash')
    parser.add_argument('--episodes', type=int, default=1000, help='Number of training episodes')
    parser.add_argument('--save-interval', type=int, default=100, help='Save model every N episodes')
    parser.add_argument('--load-model', type=str, help='Path to load existing model')
    parser.add_argument('--test-mode', action='store_true', help='Run in test mode (no training)')
    parser.add_argument('--preset', type=str, help='Configuration preset (fast_learning, stable_learning, etc.)')
    parser.add_argument('--show-config', action='store_true', help='Show configuration and exit')

    args = parser.parse_args()

    print("🎮 Geometry Dash AI")
    print("🛡️  Emergency stop: Press ESC to stop immediately")
    print()

    # Load configuration
    config = get_config(preset=args.preset)

    # Show config and exit if requested
    if args.show_config:
        from config import print_config
        print_config(config)
        return

    # Check if CUDA is available (though we're targeting CPU)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Create environment with config
    env = GeometryDashEnv(
        frame_skip=config['env']['frame_skip'],
        max_episode_steps=config['env']['max_episode_steps'],
        reward_scale=config['env']['reward_scale']
    )
    
    # Create agent with config
    agent = DQNAgent(
        state_size=env.observation_space.shape[0],
        action_size=env.action_space.n,
        learning_rate=config['agent']['learning_rate'],
        device=device
    )

    # Apply additional agent config
    agent.gamma = config['agent']['gamma']
    agent.epsilon = config['agent']['epsilon_start']
    agent.epsilon_min = config['agent']['epsilon_min']
    agent.epsilon_decay = config['agent']['epsilon_decay']
    agent.batch_size = config['agent']['batch_size']
    agent.target_update_freq = config['agent']['target_update_freq']
    
    # Load existing model if specified
    if args.load_model:
        agent.load_model(args.load_model)
        print(f"Loaded model from {args.load_model}")
    
    # Create trainer
    trainer = Trainer(env, agent)

    try:
        if args.test_mode:
            print("🧪 Running in test mode...")
            trainer.test(episodes=10)
        else:
            print(f"🚀 Starting training for {args.episodes} episodes...")
            trainer.train(
                episodes=args.episodes,
                save_interval=args.save_interval
            )
    except KeyboardInterrupt:
        print("\n🛑 Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        try:
            env.close()
            emergency_stop.cleanup()
        except:
            pass
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
