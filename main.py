#!/usr/bin/env python3
"""
Geometry Dash AI - Main Training Script
"""

import argparse
import torch
from geometry_dash_ai.environment import GeometryDashEnv
from geometry_dash_ai.agent import DQNAgent
from geometry_dash_ai.trainer import Trainer

def main():
    parser = argparse.ArgumentParser(description='Train AI to play Geometry Dash')
    parser.add_argument('--episodes', type=int, default=1000, help='Number of training episodes')
    parser.add_argument('--save-interval', type=int, default=100, help='Save model every N episodes')
    parser.add_argument('--load-model', type=str, help='Path to load existing model')
    parser.add_argument('--test-mode', action='store_true', help='Run in test mode (no training)')
    
    args = parser.parse_args()
    
    # Check if CUDA is available (though we're targeting CPU)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Create environment
    env = GeometryDashEnv()
    
    # Create agent
    agent = DQNAgent(
        state_size=env.observation_space.shape[0],
        action_size=env.action_space.n,
        device=device
    )
    
    # Load existing model if specified
    if args.load_model:
        agent.load_model(args.load_model)
        print(f"Loaded model from {args.load_model}")
    
    # Create trainer
    trainer = Trainer(env, agent)
    
    if args.test_mode:
        print("Running in test mode...")
        trainer.test(episodes=10)
    else:
        print(f"Starting training for {args.episodes} episodes...")
        trainer.train(
            episodes=args.episodes,
            save_interval=args.save_interval
        )

if __name__ == "__main__":
    main()
