# 🎮 Geometry Dash AI - Installation Guide

## Quick Installation (Recommended)

### 1. Run the automatic installer:
```bash
./install.sh
```

### 2. Activate the environment:
```bash
source venv/bin/activate
```

### 3. Test the system:
```bash
python test_system.py
```

---

## Manual Installation

If the automatic installer doesn't work, follow these steps:

### 1. Install System Dependencies

**Arch Linux:**
```bash
sudo pacman -S python python-pip python-venv xdotool
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv xdotool
```

**Fedora:**
```bash
sudo dnf install python3 python3-pip python3-venv xdotool
```

### 2. Create Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate
```

### 3. Upgrade pip
```bash
pip install --upgrade pip
```

### 4. Install PyTorch (CPU version)
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### 5. Install Other Dependencies
```bash
pip install -r requirements.txt
```

### 6. Create Directories
```bash
mkdir -p models plots logs
```

### 7. Test Installation
```bash
python -c "import torch; print('PyTorch version:', torch.__version__)"
python test_system.py
```

---

## Troubleshooting

### Common Issues:

**1. "ModuleNotFoundError: No module named 'torch'"**
- Make sure you activated the virtual environment: `source venv/bin/activate`
- Reinstall PyTorch: `pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu`

**2. "xdotool: command not found"**
- Install xdotool using your package manager (see system dependencies above)

**3. "Permission denied" for keyboard input**
- Make sure your user is in the `input` group
- Try running with: `sudo usermod -a -G input $USER` (then logout/login)

**4. "Could not find Geometry Dash window"**
- Make sure Geometry Dash is running and visible
- Try the manual setup option in `test_system.py`
- Use `xwininfo` to get window coordinates manually

**5. PyTorch installation fails**
- Try installing without index URL: `pip install torch torchvision`
- For older systems, try: `pip install torch==1.13.1 torchvision==0.14.1`

### Performance Issues:

**1. Training is very slow**
- Reduce `frame_skip` in config.py
- Increase `batch_size` if you have enough RAM
- Use `--preset fast_learning` for quicker convergence

**2. High CPU usage**
- Reduce `max_episode_steps` in config.py
- Increase `frame_skip` to reduce capture frequency
- Close other applications while training

---

## Verification

After installation, verify everything works:

```bash
# Activate environment
source venv/bin/activate

# Test individual components
python -c "from geometry_dash_ai.screen_capture import ScreenCapture; print('✅ Screen capture OK')"
python -c "from geometry_dash_ai.agent import DQNAgent; print('✅ DQN agent OK')"
python -c "from geometry_dash_ai.environment import GeometryDashEnv; print('✅ Environment OK')"

# Full system test
python test_system.py
```

---

## Next Steps

Once installation is complete:

1. **Open Geometry Dash** and load the "Stereo Madness" level
2. **Test the system**: `python test_system.py`
3. **Start training**: `python main.py --episodes 100`
4. **Monitor progress** in the `plots/` directory

Remember: **Press ESC anytime to stop the AI immediately!** 🛡️
