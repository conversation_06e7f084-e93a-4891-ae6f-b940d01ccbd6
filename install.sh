#!/bin/bash

# Geometry Dash AI Installation Script for Arch Linux
# This script creates a virtual environment and installs all dependencies

set -e  # Exit on any error

echo "🎮 Geometry Dash AI - Installation Script"
echo "=========================================="
echo

# Check if we're on Arch Linux
if ! command -v pacman &> /dev/null; then
    echo "⚠️  Warning: This script is designed for Arch Linux"
    echo "   You may need to install system dependencies manually"
fi

# Check Python version
echo "🐍 Checking Python version..."
python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
echo "   Found Python $python_version"

if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
    echo "❌ Python 3.8+ is required. Please upgrade Python."
    exit 1
fi

# Install system dependencies
echo
echo "📦 Installing system dependencies..."

# Check and install xdotool
if ! command -v xdotool &> /dev/null; then
    echo "   Installing xdotool..."
    if command -v pacman &> /dev/null; then
        sudo pacman -S --noconfirm xdotool
    else
        echo "   Please install xdotool manually:"
        echo "   - Ubuntu/Debian: sudo apt install xdotool"
        echo "   - Fedora: sudo dnf install xdotool"
        echo "   - Arch: sudo pacman -S xdotool"
        read -p "   Press Enter when xdotool is installed..."
    fi
else
    echo "   ✅ xdotool already installed"
fi

# Check and install other system dependencies
if command -v pacman &> /dev/null; then
    echo "   Installing additional system packages..."
    sudo pacman -S --needed --noconfirm python-pip python-venv
else
    echo "   Please ensure python3-pip and python3-venv are installed"
fi

# Create virtual environment
echo
echo "🏗️  Creating virtual environment..."
if [ -d "venv" ]; then
    echo "   Virtual environment already exists. Removing old one..."
    rm -rf venv
fi

python3 -m venv venv
echo "   ✅ Virtual environment created"

# Activate virtual environment
echo
echo "🔌 Activating virtual environment..."
source venv/bin/activate
echo "   ✅ Virtual environment activated"

# Upgrade pip
echo
echo "⬆️  Upgrading pip..."
pip install --upgrade pip
echo "   ✅ pip upgraded"

# Install Python dependencies
echo
echo "📚 Installing Python dependencies..."
echo "   This may take a few minutes..."

# Install PyTorch (CPU version)
echo "   Installing PyTorch (CPU version)..."
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# Install other dependencies
echo "   Installing other dependencies..."
pip install -r requirements.txt

echo "   ✅ All Python dependencies installed"

# Verify installation
echo
echo "🧪 Verifying installation..."

# Test imports
python3 -c "
import torch
import cv2
import mss
import pynput
import numpy as np
import gymnasium
print('✅ All core modules imported successfully')
print(f'   PyTorch version: {torch.__version__}')
print(f'   Device: {torch.device(\"cpu\")}')
"

echo
echo "🎯 Testing system components..."

# Test screen capture
python3 -c "
try:
    from geometry_dash_ai.screen_capture import ScreenCapture
    capture = ScreenCapture()
    print('✅ Screen capture module working')
except Exception as e:
    print(f'❌ Screen capture test failed: {e}')
"

# Test input controller
python3 -c "
try:
    from geometry_dash_ai.input_controller import InputController
    controller = InputController()
    print('✅ Input controller module working')
except Exception as e:
    print(f'❌ Input controller test failed: {e}')
"

# Test DQN agent
python3 -c "
try:
    from geometry_dash_ai.agent import DQNAgent
    agent = DQNAgent(state_size=9, action_size=2)
    print('✅ DQN agent module working')
except Exception as e:
    print(f'❌ DQN agent test failed: {e}')
"

echo
echo "📁 Creating directories..."
mkdir -p models plots logs
echo "   ✅ Directories created"

echo
echo "🎉 Installation completed successfully!"
echo
echo "📋 Next Steps:"
echo "   1. Activate the virtual environment:"
echo "      source venv/bin/activate"
echo
echo "   2. Open Geometry Dash and load 'Stereo Madness' level"
echo
echo "   3. Test the system:"
echo "      python test_system.py"
echo
echo "   4. Start training:"
echo "      python main.py --episodes 100"
echo
echo "🛡️  Remember: Press ESC anytime to stop the AI immediately!"
echo
echo "🔧 Troubleshooting:"
echo "   - If window detection fails, try manual setup in test_system.py"
echo "   - Make sure Geometry Dash window is visible and not minimized"
echo "   - Check that your user has permission to simulate keyboard input"
echo
echo "=========================================="
