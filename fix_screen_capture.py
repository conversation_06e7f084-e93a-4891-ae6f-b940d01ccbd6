#!/usr/bin/env python3
"""
Diagnostic and fix script for screen capture issues
"""

import os
import subprocess
import sys


def check_display_server():
    """Check if running X11 or Wayland"""
    print("🔍 Checking display server...")
    
    session_type = os.environ.get('XDG_SESSION_TYPE', 'unknown')
    wayland_display = os.environ.get('WAYLAND_DISPLAY')
    x11_display = os.environ.get('DISPLAY')
    
    print(f"   Session type: {session_type}")
    print(f"   DISPLAY: {x11_display}")
    print(f"   WAYLAND_DISPLAY: {wayland_display}")
    
    if session_type == 'wayland' or wayland_display:
        print("❌ You're running Wayland - this can cause screen capture issues")
        return 'wayland'
    elif session_type == 'x11' or x11_display:
        print("✅ You're running X11 - screen capture should work")
        return 'x11'
    else:
        print("⚠️  Unknown display server")
        return 'unknown'


def test_basic_screen_capture():
    """Test basic screen capture functionality"""
    print("\n🧪 Testing basic screen capture...")
    
    try:
        import mss
        
        with mss.mss() as sct:
            # Try to capture the entire screen
            monitor = sct.monitors[0]  # All monitors
            print(f"   Available monitors: {len(sct.monitors)}")
            print(f"   Primary monitor: {monitor}")
            
            # Try a small capture
            test_region = {
                'top': 100,
                'left': 100, 
                'width': 200,
                'height': 200
            }
            
            screenshot = sct.grab(test_region)
            print("✅ Basic screen capture works!")
            return True
            
    except Exception as e:
        print(f"❌ Basic screen capture failed: {e}")
        return False


def test_window_detection():
    """Test window detection"""
    print("\n🪟 Testing window detection...")
    
    try:
        # Test xdotool
        result = subprocess.run(['xdotool', 'search', '--name', 'Geometry'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            windows = result.stdout.strip().split('\n')
            print(f"✅ Found {len(windows)} Geometry Dash windows")
            for i, window_id in enumerate(windows):
                # Get window info
                info_result = subprocess.run(['xdotool', 'getwindowgeometry', window_id],
                                           capture_output=True, text=True)
                if info_result.returncode == 0:
                    print(f"   Window {i+1}: {info_result.stdout.strip()}")
            return True
        else:
            print("❌ No Geometry Dash windows found")
            print("   Make sure Geometry Dash is running!")
            return False
            
    except FileNotFoundError:
        print("❌ xdotool not found - install it with: sudo pacman -S xdotool")
        return False
    except Exception as e:
        print(f"❌ Window detection failed: {e}")
        return False


def provide_solutions(display_server, basic_capture_works, window_detection_works):
    """Provide specific solutions based on test results"""
    print("\n🔧 SOLUTIONS:")
    print("=" * 50)
    
    if display_server == 'wayland':
        print("\n📋 WAYLAND FIXES:")
        print("1. Switch to X11 session:")
        print("   - Log out")
        print("   - At login screen, click gear icon")
        print("   - Select 'GNOME on Xorg' or similar X11 option")
        print("   - Log back in")
        print()
        print("2. Or enable Wayland screen capture:")
        print("   - Install xdg-desktop-portal-wlr")
        print("   - May require additional setup")
    
    if not basic_capture_works:
        print("\n📋 SCREEN CAPTURE FIXES:")
        print("1. Check X11 permissions:")
        print("   xhost +local:")
        print()
        print("2. Try running with different permissions:")
        print("   sudo python test_system.py")
        print()
        print("3. Install additional packages:")
        print("   sudo pacman -S xorg-xhost")
    
    if not window_detection_works:
        print("\n📋 WINDOW DETECTION FIXES:")
        print("1. Install xdotool:")
        print("   sudo pacman -S xdotool")
        print()
        print("2. Make sure Geometry Dash is running")
        print()
        print("3. Try manual window setup:")
        print("   python test_system.py")
        print("   Choose option 3 (Manual setup)")
    
    print("\n📋 GENERAL FIXES:")
    print("1. Try the manual capture region setup:")
    print("   python test_system.py")
    print("   Select option 3 for manual setup")
    print()
    print("2. Check if running in virtual machine:")
    print("   VMs may have screen capture restrictions")
    print()
    print("3. Restart X11 session:")
    print("   Log out and log back in")


def main():
    print("🎮 Geometry Dash AI - Screen Capture Diagnostic")
    print("=" * 50)
    
    # Run diagnostics
    display_server = check_display_server()
    basic_capture_works = test_basic_screen_capture()
    window_detection_works = test_window_detection()
    
    # Provide solutions
    provide_solutions(display_server, basic_capture_works, window_detection_works)
    
    print("\n" + "=" * 50)
    print("💡 If issues persist, try the manual setup option in test_system.py")


if __name__ == "__main__":
    main()
