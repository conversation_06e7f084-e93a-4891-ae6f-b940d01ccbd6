#!/bin/bash

# Quick activation script for Geometry Dash AI

echo "🎮 Activating Geometry Dash AI environment..."

if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "   Please run ./install.sh first"
    exit 1
fi

source venv/bin/activate

echo "✅ Environment activated!"
echo
echo "🚀 Available commands:"
echo "   python main.py --episodes 100        # Start training"
echo "   python test_system.py               # Test the system"
echo "   python main.py --show-config        # Show configuration"
echo "   python main.py --test-mode          # Test trained model"
echo
echo "🛡️  Remember: Press ESC to stop anytime!"
echo

# Keep the shell open with the activated environment
exec bash
