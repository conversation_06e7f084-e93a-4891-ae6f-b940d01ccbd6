"""
Image preprocessing and feature extraction for Geometry Dash AI
"""

import cv2
import numpy as np
from typing import Tu<PERSON>, List, Optional, Dict, Any


class ImagePreprocessor:
    """Handles image preprocessing and feature extraction"""
    
    def __init__(self, target_size: Tuple[int, int] = (84, 84)):
        self.target_size = target_size
        self.player_color_range = self._get_player_color_range()
        self.obstacle_color_range = self._get_obstacle_color_range()
        
    def _get_player_color_range(self) -> Dict[str, np.ndarray]:
        """Get color range for player detection (cube/ship)"""
        # Geometry Dash player is typically bright colored
        # These ranges may need adjustment based on player skin
        return {
            'lower': np.array([0, 100, 100], dtype=np.uint8),    # Lower HSV
            'upper': np.array([180, 255, 255], dtype=np.uint8)   # Upper HSV
        }
    
    def _get_obstacle_color_range(self) -> Dict[str, np.ndarray]:
        """Get color range for obstacle detection"""
        # Obstacles are typically darker or have specific colors
        return {
            'lower': np.array([0, 0, 0], dtype=np.uint8),      # Lower HSV
            'upper': np.array([180, 255, 100], dtype=np.uint8) # Upper HSV
        }
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Main preprocessing pipeline
        Returns processed frame ready for neural network
        """
        if frame is None:
            return np.zeros((*self.target_size, 1), dtype=np.float32)
        
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
        
        # Resize to target size
        resized = cv2.resize(gray, self.target_size)
        
        # Apply edge detection
        edges = cv2.Canny(resized, 50, 150)
        
        # Normalize to [0, 1]
        normalized = edges.astype(np.float32) / 255.0
        
        # Add channel dimension
        processed = np.expand_dims(normalized, axis=-1)
        
        return processed
    
    def extract_features(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Extract high-level features from frame
        Returns dictionary with various features
        """
        if frame is None:
            return self._get_empty_features()
        
        features = {}
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV)
        
        # Find player position
        player_pos = self._find_player_position(hsv)
        features['player_x'] = player_pos[0]
        features['player_y'] = player_pos[1]
        
        # Detect obstacles
        obstacles = self._detect_obstacles(hsv)
        features['obstacles'] = obstacles
        
        # Calculate distance sensors
        distances = self._calculate_distance_sensors(hsv, player_pos)
        features.update(distances)
        
        # Detect ground/ceiling
        ground_height = self._detect_ground_height(hsv)
        features['ground_height'] = ground_height
        
        return features
    
    def _find_player_position(self, hsv_frame: np.ndarray) -> Tuple[int, int]:
        """Find player position in the frame"""
        height, width = hsv_frame.shape[:2]
        
        # Player is typically on the left side of screen
        # Look in the left 30% of the screen
        search_width = int(width * 0.3)
        search_region = hsv_frame[:, :search_width]
        
        # Create mask for bright objects (potential player)
        mask = cv2.inRange(search_region, 
                          np.array([0, 50, 200]), 
                          np.array([180, 255, 255]))
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Get largest contour (likely the player)
            largest_contour = max(contours, key=cv2.contourArea)
            M = cv2.moments(largest_contour)
            
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy)
        
        # Default position if player not found
        return (int(width * 0.15), int(height * 0.7))
    
    def _detect_obstacles(self, hsv_frame: np.ndarray) -> List[Dict[str, int]]:
        """Detect obstacles in the frame"""
        height, width = hsv_frame.shape[:2]
        
        # Look in the right side of screen (upcoming obstacles)
        search_region = hsv_frame[:, int(width * 0.3):]
        
        # Create mask for dark objects (potential obstacles)
        mask = cv2.inRange(search_region,
                          np.array([0, 0, 0]),
                          np.array([180, 255, 80]))
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        obstacles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Filter small noise
                x, y, w, h = cv2.boundingRect(contour)
                obstacles.append({
                    'x': x + int(width * 0.3),  # Adjust for search region offset
                    'y': y,
                    'width': w,
                    'height': h,
                    'area': area
                })
        
        return obstacles
    
    def _calculate_distance_sensors(self, hsv_frame: np.ndarray, 
                                  player_pos: Tuple[int, int]) -> Dict[str, float]:
        """Calculate distance sensors in multiple directions"""
        height, width = hsv_frame.shape[:2]
        player_x, player_y = player_pos
        
        # Create binary mask for obstacles
        obstacle_mask = cv2.inRange(hsv_frame,
                                   np.array([0, 0, 0]),
                                   np.array([180, 255, 80]))
        
        distances = {}
        
        # Right sensor (most important for Geometry Dash)
        distances['right'] = self._cast_ray(obstacle_mask, player_x, player_y, 1, 0, width)
        
        # Diagonal sensors
        distances['right_up'] = self._cast_ray(obstacle_mask, player_x, player_y, 1, -1, 
                                              min(width - player_x, player_y))
        distances['right_down'] = self._cast_ray(obstacle_mask, player_x, player_y, 1, 1,
                                                min(width - player_x, height - player_y))
        
        # Up and down sensors
        distances['up'] = self._cast_ray(obstacle_mask, player_x, player_y, 0, -1, player_y)
        distances['down'] = self._cast_ray(obstacle_mask, player_x, player_y, 0, 1, 
                                          height - player_y)
        
        # Normalize distances to [0, 1]
        max_distance = max(width, height)
        for key in distances:
            distances[key] = distances[key] / max_distance
        
        return distances
    
    def _cast_ray(self, mask: np.ndarray, start_x: int, start_y: int,
                  dx: int, dy: int, max_distance: int) -> float:
        """Cast a ray and return distance to first obstacle"""
        for distance in range(1, max_distance):
            x = start_x + dx * distance
            y = start_y + dy * distance
            
            if x < 0 or x >= mask.shape[1] or y < 0 or y >= mask.shape[0]:
                return distance
            
            if mask[y, x] > 0:  # Hit obstacle
                return distance
        
        return max_distance
    
    def _detect_ground_height(self, hsv_frame: np.ndarray) -> float:
        """Detect ground height (normalized)"""
        height, width = hsv_frame.shape[:2]
        
        # Look for horizontal lines in bottom half
        bottom_half = hsv_frame[height//2:, :]
        
        # Create mask for dark objects
        mask = cv2.inRange(bottom_half,
                          np.array([0, 0, 0]),
                          np.array([180, 255, 80]))
        
        # Find horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 1))
        horizontal_lines = cv2.morphologyEx(mask, cv2.MORPH_OPEN, horizontal_kernel)
        
        # Find the topmost horizontal line (ground level)
        for y in range(horizontal_lines.shape[0]):
            if np.any(horizontal_lines[y, :]):
                ground_y = y + height//2
                return 1.0 - (ground_y / height)  # Normalize
        
        return 0.3  # Default ground height
    
    def _get_empty_features(self) -> Dict[str, Any]:
        """Return empty features when frame is None"""
        return {
            'player_x': 0,
            'player_y': 0,
            'obstacles': [],
            'right': 1.0,
            'right_up': 1.0,
            'right_down': 1.0,
            'up': 1.0,
            'down': 1.0,
            'ground_height': 0.3
        }
    
    def create_feature_vector(self, features: Dict[str, Any]) -> np.ndarray:
        """Convert features dictionary to numpy vector for neural network"""
        # Create feature vector with distance sensors and basic info
        feature_vector = [
            features['player_x'] / 1000.0,  # Normalize player position
            features['player_y'] / 1000.0,
            features['right'],
            features['right_up'],
            features['right_down'],
            features['up'],
            features['down'],
            features['ground_height'],
            len(features['obstacles']) / 10.0,  # Number of obstacles (normalized)
        ]
        
        return np.array(feature_vector, dtype=np.float32)


def test_preprocessing():
    """Test function for preprocessing"""
    # Create a dummy frame for testing
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    preprocessor = ImagePreprocessor()
    
    # Test preprocessing
    processed = preprocessor.preprocess_frame(test_frame)
    print(f"Processed frame shape: {processed.shape}")
    
    # Test feature extraction
    features = preprocessor.extract_features(test_frame)
    print(f"Extracted features: {list(features.keys())}")
    
    # Test feature vector creation
    feature_vector = preprocessor.create_feature_vector(features)
    print(f"Feature vector shape: {feature_vector.shape}")
    print(f"Feature vector: {feature_vector}")


if __name__ == "__main__":
    test_preprocessing()
