"""
Input controller for Geometry Dash AI
Handles keyboard input simulation
"""

import time
from pynput import keyboard
from pynput.keyboard import Key
import threading
from typing import Optional


class InputController:
    """Handles keyboard input simulation for Geometry Dash"""
    
    def __init__(self):
        self.keyboard_controller = keyboard.Controller()
        self.is_holding = False
        self.hold_thread = None
        self.stop_holding = threading.Event()
        
    def press_space(self):
        """Press spacebar once (single jump)"""
        try:
            self.keyboard_controller.press(Key.space)
            time.sleep(0.01)  # Very short press
            self.keyboard_controller.release(Key.space)
        except Exception as e:
            print(f"Error pressing space: {e}")
    
    def start_holding_space(self):
        """Start holding spacebar (continuous hold)"""
        if self.is_holding:
            return
            
        self.is_holding = True
        self.stop_holding.clear()
        
        try:
            self.keyboard_controller.press(Key.space)
        except Exception as e:
            print(f"Error starting hold: {e}")
            self.is_holding = False
    
    def stop_holding_space(self):
        """Stop holding spacebar"""
        if not self.is_holding:
            return
            
        self.is_holding = False
        self.stop_holding.set()
        
        try:
            self.keyboard_controller.release(Key.space)
        except Exception as e:
            print(f"Error stopping hold: {e}")
    
    def execute_action(self, action: int):
        """
        Execute action based on integer input
        0: Release/Don't hold
        1: Hold spacebar
        """
        if action == 1:
            if not self.is_holding:
                self.start_holding_space()
        else:  # action == 0
            if self.is_holding:
                self.stop_holding_space()
    
    def get_current_state(self) -> int:
        """Get current input state (0: not holding, 1: holding)"""
        return 1 if self.is_holding else 0
    
    def reset(self):
        """Reset input state (release all keys)"""
        if self.is_holding:
            self.stop_holding_space()
    
    def cleanup(self):
        """Cleanup resources"""
        self.reset()


class SafeInputController(InputController):
    """
    Safe version of InputController with additional safety features
    """
    
    def __init__(self, max_hold_time: float = 5.0):
        super().__init__()
        self.max_hold_time = max_hold_time
        self.hold_start_time = None
        self.safety_enabled = True
        
    def start_holding_space(self):
        """Start holding spacebar with safety timeout"""
        if self.is_holding:
            return
            
        # Safety check: don't hold for too long
        if self.safety_enabled:
            self.hold_start_time = time.time()
            
        super().start_holding_space()
        
        # Start safety timer
        if self.safety_enabled:
            self._start_safety_timer()
    
    def _start_safety_timer(self):
        """Start safety timer to prevent infinite holds"""
        def safety_check():
            time.sleep(self.max_hold_time)
            if self.is_holding and self.hold_start_time:
                elapsed = time.time() - self.hold_start_time
                if elapsed >= self.max_hold_time:
                    print(f"Safety timeout: releasing spacebar after {elapsed:.2f}s")
                    self.stop_holding_space()
        
        timer_thread = threading.Thread(target=safety_check, daemon=True)
        timer_thread.start()
    
    def stop_holding_space(self):
        """Stop holding spacebar and reset timer"""
        super().stop_holding_space()
        self.hold_start_time = None
    
    def get_hold_duration(self) -> float:
        """Get current hold duration in seconds"""
        if self.is_holding and self.hold_start_time:
            return time.time() - self.hold_start_time
        return 0.0
    
    def set_safety_enabled(self, enabled: bool):
        """Enable or disable safety features"""
        self.safety_enabled = enabled


def test_input_controller():
    """Test function for input controller"""
    print("Testing Input Controller...")
    print("Make sure Geometry Dash is focused!")
    
    controller = SafeInputController()
    
    try:
        print("Testing single press...")
        controller.press_space()
        time.sleep(1)
        
        print("Testing hold for 2 seconds...")
        controller.start_holding_space()
        time.sleep(2)
        controller.stop_holding_space()
        
        print("Testing action interface...")
        time.sleep(1)
        
        # Test action interface
        controller.execute_action(1)  # Start holding
        time.sleep(1)
        controller.execute_action(0)  # Stop holding
        
        print("Test completed successfully!")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        controller.cleanup()


if __name__ == "__main__":
    test_input_controller()
