"""
DQN Agent for Geometry Dash AI
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
from collections import deque
from typing import Tuple, List, Optional
import os
from .input_controller import emergency_stop


class DQNNetwork(nn.Module):
    """Deep Q-Network for Geometry Dash"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 128):
        super(DQNNetwork, self).__init__()
        
        self.state_size = state_size
        self.action_size = action_size
        
        # Simple fully connected network (CPU-friendly)
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc4 = nn.Linear(hidden_size // 2, action_size)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        """Forward pass through the network"""
        x = <PERSON>.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        return x


class ReplayBuffer:
    """Experience replay buffer for DQN"""
    
    def __init__(self, capacity: int = 10000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """Add experience to buffer"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int) -> Tuple:
        """Sample batch of experiences"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)


class DQNAgent:
    """DQN Agent for Geometry Dash"""
    
    def __init__(self, state_size: int, action_size: int = 2, 
                 learning_rate: float = 0.001, device: str = 'cpu'):
        self.state_size = state_size
        self.action_size = action_size
        self.device = device
        
        # Hyperparameters
        self.learning_rate = learning_rate
        self.gamma = 0.95  # Discount factor
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.batch_size = 32
        self.target_update_freq = 100  # Update target network every N steps
        
        # Networks
        self.q_network = DQNNetwork(state_size, action_size).to(device)
        self.target_network = DQNNetwork(state_size, action_size).to(device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
        
        # Initialize target network
        self.update_target_network()
        
        # Experience replay
        self.memory = ReplayBuffer(capacity=10000)
        self.steps_done = 0
        
        # Training metrics
        self.losses = []
        
    def update_target_network(self):
        """Copy weights from main network to target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.push(state, action, reward, next_state, done)
    
    def act(self, state, training: bool = True) -> int:
        """Choose action using epsilon-greedy policy"""
        # Check for emergency stop
        if emergency_stop.is_stop_requested():
            raise KeyboardInterrupt("Emergency stop requested")

        if training and random.random() <= self.epsilon:
            return random.choice(range(self.action_size))
        
        # Convert state to tensor
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        # Get Q-values
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        
        return q_values.cpu().data.numpy().argmax()
    
    def replay(self) -> Optional[float]:
        """Train the network on a batch of experiences"""
        if len(self.memory) < self.batch_size:
            return None
        
        # Sample batch
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        # Convert to tensors
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)
        
        # Current Q-values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q-values from target network
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        
        self.optimizer.step()
        
        # Update target network periodically
        self.steps_done += 1
        if self.steps_done % self.target_update_freq == 0:
            self.update_target_network()
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # Store loss for monitoring
        loss_value = loss.item()
        self.losses.append(loss_value)
        
        return loss_value
    
    def save_model(self, filepath: str):
        """Save the model"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'steps_done': self.steps_done,
            'state_size': self.state_size,
            'action_size': self.action_size
        }, filepath)
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load the model"""
        if not os.path.exists(filepath):
            print(f"Model file {filepath} not found")
            return False
        
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.steps_done = checkpoint['steps_done']
        
        print(f"Model loaded from {filepath}")
        print(f"Epsilon: {self.epsilon:.4f}, Steps: {self.steps_done}")
        return True
    
    def get_stats(self) -> dict:
        """Get training statistics"""
        return {
            'epsilon': self.epsilon,
            'steps_done': self.steps_done,
            'memory_size': len(self.memory),
            'avg_loss': np.mean(self.losses[-100:]) if self.losses else 0.0
        }
    
    def set_training_mode(self, training: bool):
        """Set training mode"""
        if training:
            self.q_network.train()
        else:
            self.q_network.eval()


def test_agent():
    """Test function for DQN agent"""
    print("Testing DQN Agent...")
    
    # Create agent
    state_size = 9  # Example state size
    agent = DQNAgent(state_size=state_size, action_size=2)
    
    # Test action selection
    test_state = np.random.random(state_size)
    action = agent.act(test_state)
    print(f"Test state: {test_state}")
    print(f"Selected action: {action}")
    
    # Test experience storage and replay
    for i in range(100):
        state = np.random.random(state_size)
        action = random.choice([0, 1])
        reward = random.random()
        next_state = np.random.random(state_size)
        done = random.choice([True, False])
        
        agent.remember(state, action, reward, next_state, done)
    
    # Test training
    loss = agent.replay()
    print(f"Training loss: {loss}")
    
    # Test model saving/loading
    agent.save_model('test_model.pth')
    agent.load_model('test_model.pth')
    
    print("Agent test completed successfully!")


if __name__ == "__main__":
    test_agent()
