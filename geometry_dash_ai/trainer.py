"""
Training loop for Geometry Dash AI
"""

import time
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any
import os
from datetime import datetime

from .environment import GeometryDashEnv
from .agent import DQNAgent
from .input_controller import emergency_stop


class Trainer:
    """Training manager for Geometry Dash AI"""
    
    def __init__(self, env: GeometryDashEnv, agent: DQNAgent):
        self.env = env
        self.agent = agent
        
        # Training metrics
        self.episode_rewards = []
        self.episode_lengths = []
        self.survival_times = []
        self.losses = []
        
        # Training parameters
        self.print_interval = 10
        self.plot_interval = 50
        self.save_interval = 100
        
        # Create directories
        self.models_dir = "models"
        self.plots_dir = "plots"
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(self.plots_dir, exist_ok=True)
        
    def train(self, episodes: int, save_interval: int = 100):
        """Main training loop"""
        print(f"🚀 Starting training for {episodes} episodes")
        print("🛡️  Emergency stop: Press ESC to stop immediately")
        
        self.save_interval = save_interval
        best_survival_time = 0.0
        
        try:
            for episode in range(episodes):
                # Check for emergency stop
                if emergency_stop.is_stop_requested():
                    print("🚨 Emergency stop requested - ending training")
                    break
                
                episode_reward, episode_length, survival_time = self._run_episode(episode)
                
                # Store metrics
                self.episode_rewards.append(episode_reward)
                self.episode_lengths.append(episode_length)
                self.survival_times.append(survival_time)
                
                # Save best model
                if survival_time > best_survival_time:
                    best_survival_time = survival_time
                    self.agent.save_model(f"{self.models_dir}/best_model.pth")
                    print(f"🏆 New best survival time: {survival_time:.2f}s")
                
                # Print progress
                if episode % self.print_interval == 0:
                    self._print_progress(episode, episodes)
                
                # Plot progress
                if episode % self.plot_interval == 0 and episode > 0:
                    self._plot_progress()
                
                # Save checkpoint
                if episode % save_interval == 0 and episode > 0:
                    self.agent.save_model(f"{self.models_dir}/checkpoint_ep{episode}.pth")
                    print(f"💾 Checkpoint saved at episode {episode}")
        
        except KeyboardInterrupt:
            print("\n🛑 Training interrupted by user")
        except Exception as e:
            print(f"\n❌ Training error: {e}")
        finally:
            # Final save
            self.agent.save_model(f"{self.models_dir}/final_model.pth")
            self._plot_progress()
            self._save_training_log()
            self.env.close()
            print("🏁 Training completed")
    
    def _run_episode(self, episode: int) -> tuple:
        """Run a single training episode"""
        state, _ = self.env.reset()
        total_reward = 0.0
        steps = 0
        
        while True:
            # Check for emergency stop
            if emergency_stop.is_stop_requested():
                break
            
            # Choose action
            action = self.agent.act(state, training=True)
            
            # Take step
            next_state, reward, terminated, truncated, info = self.env.step(action)
            
            # Store experience
            self.agent.remember(state, action, reward, next_state, terminated or truncated)
            
            # Train agent
            if len(self.agent.memory) > self.agent.batch_size:
                loss = self.agent.replay()
                if loss is not None:
                    self.losses.append(loss)
            
            # Update state
            state = next_state
            total_reward += reward
            steps += 1
            
            # Check if episode is done
            if terminated or truncated:
                break
        
        survival_time = info.get('survival_time', 0.0)
        return total_reward, steps, survival_time
    
    def _print_progress(self, episode: int, total_episodes: int):
        """Print training progress"""
        if not self.episode_rewards:
            return
        
        recent_rewards = self.episode_rewards[-self.print_interval:]
        recent_survival = self.survival_times[-self.print_interval:]
        recent_lengths = self.episode_lengths[-self.print_interval:]
        
        avg_reward = np.mean(recent_rewards)
        avg_survival = np.mean(recent_survival)
        avg_length = np.mean(recent_lengths)
        max_survival = max(self.survival_times)
        
        agent_stats = self.agent.get_stats()
        
        print(f"\n📊 Episode {episode}/{total_episodes}")
        print(f"   Avg Reward: {avg_reward:.2f}")
        print(f"   Avg Survival: {avg_survival:.2f}s")
        print(f"   Max Survival: {max_survival:.2f}s")
        print(f"   Avg Length: {avg_length:.0f} steps")
        print(f"   Epsilon: {agent_stats['epsilon']:.3f}")
        print(f"   Memory: {agent_stats['memory_size']}")
        if agent_stats['avg_loss'] > 0:
            print(f"   Avg Loss: {agent_stats['avg_loss']:.4f}")
    
    def _plot_progress(self):
        """Plot training progress"""
        if len(self.episode_rewards) < 2:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Geometry Dash AI Training Progress')
        
        # Episode rewards
        axes[0, 0].plot(self.episode_rewards)
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Total Reward')
        
        # Survival times
        axes[0, 1].plot(self.survival_times)
        axes[0, 1].set_title('Survival Times')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Survival Time (s)')
        
        # Episode lengths
        axes[1, 0].plot(self.episode_lengths)
        axes[1, 0].set_title('Episode Lengths')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Steps')
        
        # Training losses
        if self.losses:
            axes[1, 1].plot(self.losses)
            axes[1, 1].set_title('Training Loss')
            axes[1, 1].set_xlabel('Training Step')
            axes[1, 1].set_ylabel('Loss')
        else:
            axes[1, 1].text(0.5, 0.5, 'No loss data yet', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Training Loss')
        
        plt.tight_layout()
        
        # Save plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f"{self.plots_dir}/training_progress_{timestamp}.png")
        plt.close()
    
    def _save_training_log(self):
        """Save training log to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"training_log_{timestamp}.txt"
        
        with open(log_file, 'w') as f:
            f.write("Geometry Dash AI Training Log\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"Total Episodes: {len(self.episode_rewards)}\n")
            f.write(f"Max Survival Time: {max(self.survival_times) if self.survival_times else 0:.2f}s\n")
            f.write(f"Avg Survival Time: {np.mean(self.survival_times) if self.survival_times else 0:.2f}s\n")
            f.write(f"Max Episode Reward: {max(self.episode_rewards) if self.episode_rewards else 0:.2f}\n")
            f.write(f"Avg Episode Reward: {np.mean(self.episode_rewards) if self.episode_rewards else 0:.2f}\n")
            f.write("\nAgent Statistics:\n")
            agent_stats = self.agent.get_stats()
            for key, value in agent_stats.items():
                f.write(f"  {key}: {value}\n")
        
        print(f"📝 Training log saved to {log_file}")
    
    def test(self, episodes: int = 10, render: bool = True):
        """Test the trained agent"""
        print(f"🧪 Testing agent for {episodes} episodes")
        
        self.agent.set_training_mode(False)  # Disable training mode
        test_survival_times = []
        
        try:
            for episode in range(episodes):
                if emergency_stop.is_stop_requested():
                    break
                
                state, _ = self.env.reset()
                total_reward = 0.0
                steps = 0
                
                while True:
                    if emergency_stop.is_stop_requested():
                        break
                    
                    # Choose action (no exploration)
                    action = self.agent.act(state, training=False)
                    
                    # Take step
                    state, reward, terminated, truncated, info = self.env.step(action)
                    total_reward += reward
                    steps += 1
                    
                    # Render if requested
                    if render:
                        self.env.render()
                    
                    if terminated or truncated:
                        break
                
                survival_time = info.get('survival_time', 0.0)
                test_survival_times.append(survival_time)
                
                print(f"Test Episode {episode + 1}: "
                      f"Survival={survival_time:.2f}s, "
                      f"Reward={total_reward:.2f}, "
                      f"Steps={steps}")
        
        except KeyboardInterrupt:
            print("\n🛑 Testing interrupted by user")
        finally:
            self.env.close()
            
            if test_survival_times:
                print(f"\n📈 Test Results:")
                print(f"   Avg Survival: {np.mean(test_survival_times):.2f}s")
                print(f"   Max Survival: {max(test_survival_times):.2f}s")
                print(f"   Min Survival: {min(test_survival_times):.2f}s")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics"""
        if not self.episode_rewards:
            return {}
        
        return {
            'total_episodes': len(self.episode_rewards),
            'max_survival_time': max(self.survival_times),
            'avg_survival_time': np.mean(self.survival_times),
            'max_reward': max(self.episode_rewards),
            'avg_reward': np.mean(self.episode_rewards),
            'total_training_steps': sum(self.episode_lengths),
        }


def test_trainer():
    """Test function for trainer"""
    from .environment import GeometryDashEnv
    from .agent import DQNAgent
    
    print("Testing Trainer...")
    
    # Create environment and agent
    env = GeometryDashEnv()
    agent = DQNAgent(state_size=9, action_size=2)
    
    # Create trainer
    trainer = Trainer(env, agent)
    
    # Test short training
    try:
        trainer.train(episodes=5)
        stats = trainer.get_training_stats()
        print(f"Training stats: {stats}")
    except Exception as e:
        print(f"Training test error: {e}")
    finally:
        env.close()


if __name__ == "__main__":
    test_trainer()
