"""
Screen capture system for Geometry Dash AI
Handles window detection and screen capture
"""

import mss
import numpy as np
import cv2
import psutil
import time
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any


class ScreenCapture:
    """Handles screen capture and window detection for Geometry Dash"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.window_info = None
        self.capture_region = None
        
    def find_geometry_dash_window(self) -> bool:
        """
        Find Geometry Dash window automatically
        Returns True if found, False otherwise
        """
        try:
            # Get all windows using xdotool (Linux)
            import subprocess
            
            # Find window by title containing "Geometry Dash"
            result = subprocess.run(
                ['xdotool', 'search', '--name', 'Geometry Dash'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                window_id = result.stdout.strip().split('\n')[0]
                
                # Get window geometry
                geom_result = subprocess.run(
                    ['xdotool', 'getwindowgeometry', window_id],
                    capture_output=True, text=True
                )
                
                if geom_result.returncode == 0:
                    lines = geom_result.stdout.strip().split('\n')
                    for line in lines:
                        if 'Position:' in line:
                            pos = line.split('Position:')[1].strip().split(',')
                            x = int(pos[0])
                            y = int(pos[1])
                        elif 'Geometry:' in line:
                            geom = line.split('Geometry:')[1].strip().split('x')
                            width = int(geom[0])
                            height = int(geom[1])
                    
                    self.window_info = {
                        'window_id': window_id,
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height
                    }
                    
                    # Set capture region
                    self.capture_region = {
                        'top': y,
                        'left': x,
                        'width': width,
                        'height': height
                    }
                    
                    print(f"Found Geometry Dash window: {width}x{height} at ({x}, {y})")
                    return True
                    
        except Exception as e:
            print(f"Error finding window with xdotool: {e}")
            
        # Fallback: try to find by process name
        return self._find_by_process()
    
    def _find_by_process(self) -> bool:
        """Fallback method to find window by process name"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'geometry' in proc.info['name'].lower():
                    print(f"Found Geometry Dash process: {proc.info['name']}")
                    # For now, use full screen capture as fallback
                    monitor = self.sct.monitors[1]  # Primary monitor
                    self.capture_region = monitor
                    self.window_info = {'fallback': True}
                    return True
        except Exception as e:
            print(f"Error finding by process: {e}")
            
        return False
    
    def set_manual_region(self, x: int, y: int, width: int, height: int):
        """Manually set capture region"""
        self.capture_region = {
            'top': y,
            'left': x,
            'width': width,
            'height': height
        }
        self.window_info = {
            'manual': True,
            'x': x,
            'y': y,
            'width': width,
            'height': height
        }
        print(f"Manual capture region set: {width}x{height} at ({x}, {y})")
    
    def capture_screen(self) -> Optional[np.ndarray]:
        """
        Capture the current screen region
        Returns numpy array of the captured image or None if failed
        """
        if not self.capture_region:
            if not self.find_geometry_dash_window():
                print("No capture region set and couldn't find window")
                return None
        
        try:
            # Capture the screen
            screenshot = self.sct.grab(self.capture_region)
            
            # Convert to numpy array
            img = np.array(screenshot)
            
            # Convert from BGRA to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)
            
            return img
            
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None
    
    def get_window_info(self) -> Optional[Dict[str, Any]]:
        """Get current window information"""
        return self.window_info
    
    def is_window_active(self) -> bool:
        """Check if the Geometry Dash window is still active/visible"""
        if not self.window_info:
            return False
            
        if 'window_id' in self.window_info:
            try:
                import subprocess
                result = subprocess.run(
                    ['xdotool', 'getwindowgeometry', self.window_info['window_id']],
                    capture_output=True, text=True
                )
                return result.returncode == 0
            except:
                return False
        
        return True  # Assume active for fallback methods
    
    def focus_window(self) -> bool:
        """Focus the Geometry Dash window"""
        if not self.window_info or 'window_id' not in self.window_info:
            return False
            
        try:
            import subprocess
            subprocess.run(
                ['xdotool', 'windowactivate', self.window_info['window_id']],
                check=True
            )
            time.sleep(0.1)  # Small delay for window to focus
            return True
        except Exception as e:
            print(f"Error focusing window: {e}")
            return False


def test_screen_capture():
    """Test function for screen capture"""
    capture = ScreenCapture()
    
    if capture.find_geometry_dash_window():
        print("Window found successfully!")
        
        # Test capture
        img = capture.capture_screen()
        if img is not None:
            print(f"Captured image shape: {img.shape}")
            
            # Save test image
            cv2.imwrite('test_capture.png', cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            print("Test capture saved as 'test_capture.png'")
        else:
            print("Failed to capture screen")
    else:
        print("Could not find Geometry Dash window")
        print("You can set manual region using set_manual_region(x, y, width, height)")


if __name__ == "__main__":
    test_screen_capture()
