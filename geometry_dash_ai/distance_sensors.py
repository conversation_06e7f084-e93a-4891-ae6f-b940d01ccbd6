"""
Distance sensor system for Geometry Dash AI
Provides distance-based obstacle detection
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional
import math


class DistanceSensorSystem:
    """Advanced distance sensor system for obstacle detection"""
    
    def __init__(self, num_rays: int = 8, max_distance: int = 200):
        self.num_rays = num_rays
        self.max_distance = max_distance
        self.sensor_angles = self._calculate_sensor_angles()
        
    def _calculate_sensor_angles(self) -> List[float]:
        """Calculate angles for distance sensors"""
        angles = []
        
        # Primary forward sensors (most important for Geometry Dash)
        angles.extend([0, -15, 15, -30, 30])  # Forward, slight up/down, more up/down
        
        # Additional sensors if needed
        if self.num_rays > 5:
            angles.extend([-45, 45, -60])  # Diagonal sensors
        
        # Convert to radians
        return [math.radians(angle) for angle in angles[:self.num_rays]]
    
    def get_distance_readings(self, frame: np.ndarray, 
                            player_pos: Tuple[int, int]) -> Dict[str, float]:
        """
        Get distance readings from all sensors
        Returns normalized distances (0.0 = obstacle at player, 1.0 = no obstacle in range)
        """
        if frame is None:
            return self._get_empty_readings()
        
        # Convert to binary obstacle mask
        obstacle_mask = self._create_obstacle_mask(frame)
        
        distances = {}
        player_x, player_y = player_pos
        
        for i, angle in enumerate(self.sensor_angles):
            distance = self._cast_ray(obstacle_mask, player_x, player_y, angle)
            distances[f'sensor_{i}'] = distance / self.max_distance
        
        # Add specific named sensors for important directions
        distances['forward'] = distances.get('sensor_0', 1.0)
        distances['forward_up'] = distances.get('sensor_1', 1.0)
        distances['forward_down'] = distances.get('sensor_2', 1.0)
        
        return distances
    
    def _create_obstacle_mask(self, frame: np.ndarray) -> np.ndarray:
        """Create binary mask for obstacles"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV)
        
        # Create mask for dark objects (obstacles)
        lower_dark = np.array([0, 0, 0])
        upper_dark = np.array([180, 255, 80])
        dark_mask = cv2.inRange(hsv, lower_dark, upper_dark)
        
        # Create mask for specific obstacle colors (spikes, blocks)
        # Adjust these ranges based on the level's color scheme
        lower_obstacle = np.array([0, 50, 50])
        upper_obstacle = np.array([20, 255, 255])
        obstacle_mask = cv2.inRange(hsv, lower_obstacle, upper_obstacle)
        
        # Combine masks
        combined_mask = cv2.bitwise_or(dark_mask, obstacle_mask)
        
        # Apply morphological operations to clean up the mask
        kernel = np.ones((3, 3), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        
        return combined_mask
    
    def _cast_ray(self, mask: np.ndarray, start_x: int, start_y: int, 
                  angle: float) -> float:
        """Cast a ray at given angle and return distance to first obstacle"""
        dx = math.cos(angle)
        dy = math.sin(angle)
        
        for distance in range(1, self.max_distance + 1):
            x = int(start_x + dx * distance)
            y = int(start_y + dy * distance)
            
            # Check bounds
            if x < 0 or x >= mask.shape[1] or y < 0 or y >= mask.shape[0]:
                return distance
            
            # Check for obstacle
            if mask[y, x] > 0:
                return distance
        
        return self.max_distance
    
    def _get_empty_readings(self) -> Dict[str, float]:
        """Return empty readings when frame is None"""
        readings = {}
        for i in range(self.num_rays):
            readings[f'sensor_{i}'] = 1.0
        
        readings.update({
            'forward': 1.0,
            'forward_up': 1.0,
            'forward_down': 1.0
        })
        
        return readings
    
    def visualize_sensors(self, frame: np.ndarray, player_pos: Tuple[int, int],
                         distances: Dict[str, float]) -> np.ndarray:
        """
        Visualize distance sensors on the frame for debugging
        Returns frame with sensor rays drawn
        """
        if frame is None:
            return np.zeros((480, 640, 3), dtype=np.uint8)
        
        vis_frame = frame.copy()
        player_x, player_y = player_pos
        
        # Draw player position
        cv2.circle(vis_frame, (player_x, player_y), 5, (0, 255, 0), -1)
        
        # Draw sensor rays
        for i, angle in enumerate(self.sensor_angles):
            sensor_key = f'sensor_{i}'
            if sensor_key in distances:
                distance = distances[sensor_key] * self.max_distance
                
                # Calculate end point
                dx = math.cos(angle)
                dy = math.sin(angle)
                end_x = int(player_x + dx * distance)
                end_y = int(player_y + dy * distance)
                
                # Choose color based on distance
                if distances[sensor_key] < 0.3:
                    color = (0, 0, 255)  # Red for close obstacles
                elif distances[sensor_key] < 0.6:
                    color = (0, 165, 255)  # Orange for medium distance
                else:
                    color = (0, 255, 0)  # Green for far/no obstacles
                
                # Draw ray
                cv2.line(vis_frame, (player_x, player_y), (end_x, end_y), color, 2)
                
                # Draw distance text
                text_x = int(player_x + dx * 30)
                text_y = int(player_y + dy * 30)
                cv2.putText(vis_frame, f'{distances[sensor_key]:.2f}', 
                           (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        return vis_frame


class GeometryDashSensors(DistanceSensorSystem):
    """Specialized sensor system for Geometry Dash"""
    
    def __init__(self):
        # Geometry Dash specific configuration
        super().__init__(num_rays=6, max_distance=150)
        
        # Override angles for Geometry Dash specific needs
        self.sensor_angles = [
            0,           # Forward
            -0.26,       # Forward-up (15 degrees)
            0.26,        # Forward-down (15 degrees)
            -0.52,       # Up (30 degrees)
            0.52,        # Down (30 degrees)
            -0.79        # Sharp up (45 degrees)
        ]
    
    def get_gameplay_features(self, frame: np.ndarray, 
                            player_pos: Tuple[int, int]) -> np.ndarray:
        """
        Get features specifically designed for Geometry Dash gameplay
        Returns numpy array suitable for neural network input
        """
        distances = self.get_distance_readings(frame, player_pos)
        
        # Create feature vector with most important sensors
        features = [
            distances['forward'],      # Most important: what's ahead
            distances['forward_up'],   # Can we jump over it?
            distances['forward_down'], # Is there a pit?
            distances.get('sensor_3', 1.0),  # Up sensor
            distances.get('sensor_4', 1.0),  # Down sensor
            distances.get('sensor_5', 1.0),  # Sharp up sensor
        ]
        
        # Add derived features
        features.extend([
            min(distances['forward'], distances['forward_up']),  # Minimum forward distance
            abs(distances['forward_up'] - distances['forward_down']),  # Vertical asymmetry
            1.0 if distances['forward'] > 0.8 else 0.0,  # Clear path indicator
        ])
        
        return np.array(features, dtype=np.float32)


def test_distance_sensors():
    """Test function for distance sensors"""
    # Create test frame with some obstacles
    test_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
    
    # Add some obstacles (dark rectangles)
    cv2.rectangle(test_frame, (300, 200), (350, 300), (0, 0, 0), -1)
    cv2.rectangle(test_frame, (400, 350), (450, 400), (0, 0, 0), -1)
    
    # Test sensor system
    sensors = GeometryDashSensors()
    player_pos = (100, 250)
    
    distances = sensors.get_distance_readings(test_frame, player_pos)
    print("Distance readings:")
    for key, value in distances.items():
        print(f"  {key}: {value:.3f}")
    
    # Test gameplay features
    features = sensors.get_gameplay_features(test_frame, player_pos)
    print(f"\nGameplay features: {features}")
    
    # Visualize sensors
    vis_frame = sensors.visualize_sensors(test_frame, player_pos, distances)
    cv2.imwrite('sensor_visualization.png', cv2.cvtColor(vis_frame, cv2.COLOR_RGB2BGR))
    print("\nSensor visualization saved as 'sensor_visualization.png'")


if __name__ == "__main__":
    test_distance_sensors()
