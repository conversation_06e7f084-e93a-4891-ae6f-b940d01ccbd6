"""
Geometry Dash Environment for Reinforcement Learning
OpenAI Gym compatible environment
"""

import gymnasium as gym
from gymnasium import spaces
import numpy as np
import cv2
import time
from typing import Tuple, Dict, Any, Optional

from .screen_capture import ScreenCapture
from .preprocessing import ImagePreprocessor
from .distance_sensors import GeometryDashSensors
from .input_controller import <PERSON>In<PERSON><PERSON><PERSON>roller, emergency_stop


class GeometryDashEnv(gym.Env):
    """Geometry Dash Environment for RL training"""
    
    def __init__(self, 
                 frame_skip: int = 4,
                 max_episode_steps: int = 1000,
                 reward_scale: float = 1.0):
        super().__init__()
        
        # Environment parameters
        self.frame_skip = frame_skip
        self.max_episode_steps = max_episode_steps
        self.reward_scale = reward_scale
        
        # Components
        self.screen_capture = ScreenCapture()
        self.preprocessor = ImagePreprocessor()
        self.sensors = GeometryDashSensors()
        self.input_controller = SafeInputController()
        
        # State tracking
        self.current_step = 0
        self.episode_start_time = None
        self.last_frame = None
        self.last_features = None
        self.survival_time = 0.0
        self.is_dead = False
        
        # Action space: 0 = don't hold, 1 = hold spacebar
        self.action_space = spaces.Discrete(2)
        
        # Observation space: distance sensor readings + basic features
        # 9 features: 6 distance sensors + 3 derived features
        self.observation_space = spaces.Box(
            low=0.0, high=1.0, shape=(9,), dtype=np.float32
        )
        
        # Game state detection
        self.death_detection_enabled = True
        self.last_player_pos = None
        self.stuck_counter = 0
        self.max_stuck_frames = 30
        
    def reset(self, seed=None, options=None) -> Tuple[np.ndarray, Dict]:
        """Reset the environment"""
        super().reset(seed=seed)
        
        # Check for emergency stop
        if emergency_stop.is_stop_requested():
            raise KeyboardInterrupt("Emergency stop requested")
        
        # Reset input controller
        self.input_controller.reset()
        
        # Reset state
        self.current_step = 0
        self.episode_start_time = time.time()
        self.survival_time = 0.0
        self.is_dead = False
        self.last_player_pos = None
        self.stuck_counter = 0
        
        # Find and focus game window
        if not self.screen_capture.find_geometry_dash_window():
            print("Warning: Could not find Geometry Dash window")
        else:
            self.screen_capture.focus_window()
            time.sleep(0.5)  # Wait for window to focus
        
        # Wait for game to be ready (you might need to manually restart level)
        print("Please restart the level in Geometry Dash and press Enter...")
        input("Press Enter when ready to start training...")
        
        # Get initial observation
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """Execute one step in the environment"""
        # Check for emergency stop
        if emergency_stop.is_stop_requested():
            raise KeyboardInterrupt("Emergency stop requested")
        
        # Execute action for multiple frames (frame skipping)
        for _ in range(self.frame_skip):
            self.input_controller.execute_action(action)
            time.sleep(0.016)  # ~60 FPS timing
        
        # Get new observation
        observation = self._get_observation()
        
        # Calculate reward
        reward = self._calculate_reward()
        
        # Check if episode is done
        terminated = self._check_terminated()
        truncated = self.current_step >= self.max_episode_steps
        
        # Update step counter
        self.current_step += 1
        self.survival_time = time.time() - self.episode_start_time
        
        info = self._get_info()
        
        return observation, reward, terminated, truncated, info
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation from the game"""
        # Capture screen
        frame = self.screen_capture.capture_screen()
        
        if frame is None:
            # Return zero observation if capture failed
            return np.zeros(self.observation_space.shape, dtype=np.float32)
        
        self.last_frame = frame
        
        # Extract features
        features = self.preprocessor.extract_features(frame)
        self.last_features = features
        
        # Get player position
        player_pos = (features['player_x'], features['player_y'])
        
        # Get distance sensor readings
        sensor_features = self.sensors.get_gameplay_features(frame, player_pos)
        
        return sensor_features
    
    def _calculate_reward(self) -> float:
        """Calculate reward based on survival time and game state"""
        reward = 0.0
        
        # Base survival reward (main reward signal)
        survival_reward = 0.1 * self.reward_scale
        reward += survival_reward
        
        # Bonus for forward progress (if we can detect it)
        if self.last_features and 'player_x' in self.last_features:
            # Small bonus for maintaining position (not going backwards)
            if self.last_player_pos is not None:
                progress = self.last_features['player_x'] - self.last_player_pos[0]
                if progress >= 0:
                    reward += 0.05 * self.reward_scale
                else:
                    reward -= 0.02 * self.reward_scale  # Small penalty for going backwards
            
            self.last_player_pos = (self.last_features['player_x'], self.last_features['player_y'])
        
        # Penalty for death
        if self.is_dead:
            reward -= 10.0 * self.reward_scale
        
        # Small penalty for being stuck
        if self.stuck_counter > self.max_stuck_frames // 2:
            reward -= 0.1 * self.reward_scale
        
        return reward
    
    def _check_terminated(self) -> bool:
        """Check if the episode should terminate (player died)"""
        if not self.death_detection_enabled:
            return False
        
        # Simple death detection: check if player position hasn't changed
        if self.last_features and self.last_player_pos:
            current_pos = (self.last_features['player_x'], self.last_features['player_y'])
            
            # If position hasn't changed significantly
            pos_diff = abs(current_pos[0] - self.last_player_pos[0]) + abs(current_pos[1] - self.last_player_pos[1])
            
            if pos_diff < 5:  # Threshold for "stuck"
                self.stuck_counter += 1
            else:
                self.stuck_counter = 0
            
            # If stuck for too long, assume death
            if self.stuck_counter > self.max_stuck_frames:
                self.is_dead = True
                return True
        
        # Additional death detection could be added here
        # (e.g., checking for specific colors, game over screen, etc.)
        
        return False
    
    def _get_info(self) -> Dict[str, Any]:
        """Get additional information about the environment state"""
        info = {
            'survival_time': self.survival_time,
            'current_step': self.current_step,
            'is_dead': self.is_dead,
            'stuck_counter': self.stuck_counter,
            'input_state': self.input_controller.get_current_state(),
        }
        
        if self.last_features:
            info.update({
                'player_pos': (self.last_features['player_x'], self.last_features['player_y']),
                'obstacles_detected': len(self.last_features['obstacles']),
            })
        
        return info
    
    def render(self, mode='human'):
        """Render the environment (optional)"""
        if mode == 'human' and self.last_frame is not None:
            # Show the captured frame with sensor visualization
            if self.last_features:
                player_pos = (self.last_features['player_x'], self.last_features['player_y'])
                distances = {
                    'sensor_0': self.last_features.get('right', 1.0),
                    'sensor_1': self.last_features.get('right_up', 1.0),
                    'sensor_2': self.last_features.get('right_down', 1.0),
                }
                
                vis_frame = self.sensors.visualize_sensors(self.last_frame, player_pos, distances)
                
                # Add info text
                cv2.putText(vis_frame, f"Step: {self.current_step}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(vis_frame, f"Survival: {self.survival_time:.1f}s", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(vis_frame, f"Action: {self.input_controller.get_current_state()}", (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                cv2.imshow('Geometry Dash AI', cv2.cvtColor(vis_frame, cv2.COLOR_RGB2BGR))
                cv2.waitKey(1)
    
    def close(self):
        """Clean up resources"""
        self.input_controller.cleanup()
        cv2.destroyAllWindows()
    
    def set_manual_capture_region(self, x: int, y: int, width: int, height: int):
        """Manually set the screen capture region"""
        self.screen_capture.set_manual_region(x, y, width, height)
    
    def enable_death_detection(self, enabled: bool):
        """Enable or disable automatic death detection"""
        self.death_detection_enabled = enabled


def test_environment():
    """Test function for the environment"""
    print("Testing Geometry Dash Environment...")
    
    env = GeometryDashEnv()
    
    try:
        # Reset environment
        obs, info = env.reset()
        print(f"Initial observation shape: {obs.shape}")
        print(f"Initial info: {info}")
        
        # Run a few steps
        for step in range(10):
            action = np.random.choice([0, 1])  # Random action
            obs, reward, terminated, truncated, info = env.step(action)
            
            print(f"Step {step}: Action={action}, Reward={reward:.3f}, "
                  f"Terminated={terminated}, Info={info}")
            
            if terminated or truncated:
                break
        
        print("Environment test completed!")
        
    except KeyboardInterrupt:
        print("Test interrupted by user")
    finally:
        env.close()


if __name__ == "__main__":
    test_environment()
