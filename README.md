# Geometry Dash AI

A reinforcement learning AI that learns to play Geometry Dash using Deep Q-Network (DQN).

## Features

- Screen capture and window detection
- Visual preprocessing and obstacle detection
- Distance-based sensors
- DQN-based learning
- CPU-optimized training
- Spacebar input simulation

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure Geometry Dash is running with the "Stereo Madness" level loaded

3. Run training:
```bash
python main.py --episodes 1000
```

## Usage

### Training
```bash
python main.py --episodes 1000 --save-interval 100
```

### Testing
```bash
python main.py --test-mode --load-model models/best_model.pth
```

## Project Structure

- `geometry_dash_ai/` - Main package
  - `environment.py` - Game environment wrapper
  - `agent.py` - DQN agent implementation
  - `screen_capture.py` - Screen capture and window detection
  - `preprocessing.py` - Image processing and feature extraction
  - `input_controller.py` - Keyboard input simulation
  - `trainer.py` - Training loop and utilities
- `main.py` - Main training script
- `models/` - Saved model checkpoints

## Requirements

- Python 3.8+
- PyTorch
- OpenCV
- Arch Linux (tested)
- Geometry Dash running
