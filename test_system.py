#!/usr/bin/env python3
"""
System test script for Geometry Dash AI
"""

import sys
import time
import numpy as np
from geometry_dash_ai.environment import GeometryDashEnv
from geometry_dash_ai.agent import DQNAgent


def test_full_system():
    """Test the complete system integration"""
    print("🧪 Testing complete Geometry Dash AI system...")
    print("🛡️  Emergency stop: Press ESC to stop immediately")
    print()
    
    try:
        # Create environment
        print("1. Creating environment...")
        env = GeometryDashEnv()
        print("✅ Environment created")
        
        # Create agent
        print("2. Creating DQN agent...")
        agent = DQNAgent(state_size=9, action_size=2)
        print("✅ Agent created")
        
        # Test environment reset
        print("3. Testing environment reset...")
        print("   Please make sure Geometry Dash is running with Stereo Madness level")
        input("   Press Enter when ready...")
        
        obs, info = env.reset()
        print(f"✅ Environment reset successful")
        print(f"   Observation shape: {obs.shape}")
        print(f"   Initial info: {info}")
        
        # Test a few steps
        print("4. Testing agent-environment interaction...")
        total_reward = 0
        
        for step in range(20):
            # Agent chooses action
            action = agent.act(obs, training=False)
            
            # Environment step
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            
            print(f"   Step {step+1}: Action={action}, Reward={reward:.3f}, "
                  f"Survival={info.get('survival_time', 0):.1f}s")
            
            # Render (optional)
            env.render()
            
            if terminated or truncated:
                print(f"   Episode ended: Terminated={terminated}, Truncated={truncated}")
                break
            
            time.sleep(0.1)  # Small delay for observation
        
        print(f"✅ System test completed successfully!")
        print(f"   Total reward: {total_reward:.3f}")
        print(f"   Final survival time: {info.get('survival_time', 0):.1f}s")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            env.close()
        except:
            pass


def test_screen_capture_only():
    """Test just the screen capture functionality"""
    print("📸 Testing screen capture...")
    
    try:
        from geometry_dash_ai.screen_capture import ScreenCapture
        
        capture = ScreenCapture()
        
        # Try to find window
        if capture.find_geometry_dash_window():
            print("✅ Geometry Dash window found")
            
            # Test capture
            for i in range(5):
                img = capture.capture_screen()
                if img is not None:
                    print(f"✅ Capture {i+1}: {img.shape}")
                else:
                    print(f"❌ Capture {i+1}: Failed")
                time.sleep(0.5)
        else:
            print("❌ Could not find Geometry Dash window")
            print("   Make sure Geometry Dash is running and visible")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Screen capture test failed: {e}")
        return False


def test_manual_setup():
    """Test with manual window setup"""
    print("🔧 Testing with manual setup...")
    print("   This will help you set up the capture region manually")
    
    try:
        from geometry_dash_ai.screen_capture import ScreenCapture
        
        capture = ScreenCapture()
        
        print("\n📏 Manual capture region setup:")
        print("   Please provide the Geometry Dash window coordinates")
        print("   You can find these using a tool like xwininfo")
        print("   Example: xwininfo | grep geometry")
        print()
        
        try:
            x = int(input("   Window X position: "))
            y = int(input("   Window Y position: "))
            width = int(input("   Window width: "))
            height = int(input("   Window height: "))
            
            capture.set_manual_region(x, y, width, height)
            
            # Test capture
            img = capture.capture_screen()
            if img is not None:
                print(f"✅ Manual capture successful: {img.shape}")
                
                # Save test image
                import cv2
                cv2.imwrite('manual_capture_test.png', cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
                print("✅ Test image saved as 'manual_capture_test.png'")
                return True
            else:
                print("❌ Manual capture failed")
                return False
                
        except ValueError:
            print("❌ Invalid input values")
            return False
        
    except Exception as e:
        print(f"❌ Manual setup test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🎮 Geometry Dash AI - System Test")
    print("="*50)
    print()
    
    print("Choose test mode:")
    print("1. Full system test (recommended)")
    print("2. Screen capture only")
    print("3. Manual setup test")
    print("4. Exit")
    print()
    
    try:
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == "1":
            success = test_full_system()
        elif choice == "2":
            success = test_screen_capture_only()
        elif choice == "3":
            success = test_manual_setup()
        elif choice == "4":
            print("👋 Goodbye!")
            return True
        else:
            print("❌ Invalid choice")
            return False
        
        if success:
            print("\n🎉 Test completed successfully!")
            print("   You can now run the main training script:")
            print("   python main.py --episodes 100")
        else:
            print("\n❌ Test failed. Please check the error messages above.")
            print("   Try running setup.py again or check the troubleshooting guide.")
        
        return success
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
