#!/usr/bin/env python3
"""
Setup script for Geometry Dash AI
"""

import subprocess
import sys
import os


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        # Install Python packages
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python dependencies installed successfully")
        
        # Check for system dependencies (Arch Linux)
        print("🔍 Checking system dependencies...")
        
        # Check for xdotool (needed for window detection)
        try:
            subprocess.check_call(["which", "xdotool"], stdout=subprocess.DEVNULL)
            print("✅ xdotool found")
        except subprocess.CalledProcessError:
            print("❌ xdotool not found. Please install it:")
            print("   sudo pacman -S xdotool")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def test_components():
    """Test individual components"""
    print("\n🧪 Testing components...")
    
    # Test screen capture
    print("Testing screen capture...")
    try:
        from geometry_dash_ai.screen_capture import test_screen_capture
        test_screen_capture()
        print("✅ Screen capture test passed")
    except Exception as e:
        print(f"❌ Screen capture test failed: {e}")
        return False
    
    # Test input controller
    print("Testing input controller...")
    try:
        from geometry_dash_ai.input_controller import test_input_controller
        # Skip interactive test for now
        print("✅ Input controller imported successfully")
    except Exception as e:
        print(f"❌ Input controller test failed: {e}")
        return False
    
    # Test preprocessing
    print("Testing preprocessing...")
    try:
        from geometry_dash_ai.preprocessing import test_preprocessing
        test_preprocessing()
        print("✅ Preprocessing test passed")
    except Exception as e:
        print(f"❌ Preprocessing test failed: {e}")
        return False
    
    # Test distance sensors
    print("Testing distance sensors...")
    try:
        from geometry_dash_ai.distance_sensors import test_distance_sensors
        test_distance_sensors()
        print("✅ Distance sensors test passed")
    except Exception as e:
        print(f"❌ Distance sensors test failed: {e}")
        return False
    
    # Test agent
    print("Testing DQN agent...")
    try:
        from geometry_dash_ai.agent import test_agent
        test_agent()
        print("✅ DQN agent test passed")
    except Exception as e:
        print(f"❌ DQN agent test failed: {e}")
        return False
    
    return True


def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ["models", "plots", "logs"]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")


def print_usage_instructions():
    """Print usage instructions"""
    print("\n" + "="*60)
    print("🎮 GEOMETRY DASH AI - SETUP COMPLETE")
    print("="*60)
    print()
    print("📋 USAGE INSTRUCTIONS:")
    print()
    print("1. 🎯 Open Geometry Dash and load 'Stereo Madness' level")
    print("2. 🚀 Start training:")
    print("   python main.py --episodes 1000")
    print()
    print("3. 🧪 Test trained model:")
    print("   python main.py --test-mode --load-model models/best_model.pth")
    print()
    print("4. 🛡️  EMERGENCY STOP: Press ESC key anytime to stop immediately")
    print()
    print("📊 MONITORING:")
    print("   - Training progress plots saved in 'plots/' directory")
    print("   - Model checkpoints saved in 'models/' directory")
    print("   - Training logs saved as 'training_log_*.txt'")
    print()
    print("⚙️  CONFIGURATION:")
    print("   - Edit hyperparameters in geometry_dash_ai/agent.py")
    print("   - Adjust reward function in geometry_dash_ai/environment.py")
    print("   - Modify sensor configuration in geometry_dash_ai/distance_sensors.py")
    print()
    print("🔧 TROUBLESHOOTING:")
    print("   - If window detection fails, use manual capture region")
    print("   - Ensure Geometry Dash window is visible and not minimized")
    print("   - Check that xdotool is installed for window management")
    print()
    print("="*60)


def main():
    """Main setup function"""
    print("🚀 Setting up Geometry Dash AI...")
    print()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Setup failed during dependency installation")
        return False
    
    # Create directories
    create_directories()
    
    # Test components
    if not test_components():
        print("❌ Setup failed during component testing")
        return False
    
    print("\n✅ Setup completed successfully!")
    print_usage_instructions()
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
